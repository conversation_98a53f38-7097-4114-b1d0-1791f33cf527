cmake_minimum_required(VERSION 3.16)
project(VideoService VERSION 1.0.1 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Add common compiler flags
add_compile_options(-Wall -Wextra -Wpedantic)

# Project directories
set(PROJECT_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(PROJECT_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(PROJECT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config)
set(PROJECT_SCRIPTS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/scripts)
set(PROJECT_DOCS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/docs)
set(PROJECT_TEST_DIR ${CMAKE_CURRENT_SOURCE_DIR}/test)
set(PROJECT_INSTALL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/install)


# Find required packages
find_package(PkgConfig REQUIRED)

# FFmpeg (for RTSP packet capture)
pkg_check_modules(FFMPEG REQUIRED
    libavformat>=58.0
    libavcodec>=58.0
    libavutil>=56.0
)

# GStreamer (for video converter encoding/decoding and RTSP server)
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-webrtc-1.0
    gstreamer-sdp-1.0
    gstreamer-rtsp-server-1.0
)

# JsonCpp (for RTSP server configuration)
pkg_check_modules(JSONCPP jsoncpp REQUIRED)
if(JSONCPP_FOUND AND JSONCPP_LIBRARIES AND JSONCPP_INCLUDE_DIRS)
    add_definitions(-DHAVE_JSONCPP)
endif()

# Fast DDS
find_package(fastdds 3 REQUIRED)
find_package(fastcdr 2 REQUIRED)

# OpenCV (optional, for image processing)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    add_definitions(-DHAVE_OPENCV)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
    message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")

    # 检查是否使用opencv_world库
    if(OpenCV_LIBS MATCHES "opencv_world")
        message(STATUS "Using opencv_world library")
        set(OPENCV_WORLD_FOUND TRUE)
    else()
        message(STATUS "Using separate OpenCV libraries")
        set(OPENCV_WORLD_FOUND FALSE)
    endif()
endif()

# TensorRT (optional)
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES include)
find_library(TENSORRT_LIBRARY nvinfer
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib lib64 lib/x64)

if(TENSORRT_INCLUDE_DIR AND TENSORRT_LIBRARY)
    set(TENSORRT_FOUND TRUE)
    add_definitions(-DHAVE_TENSORRT)
endif()

# ONNX Runtime (optional)
find_path(ONNXRUNTIME_INCLUDE_DIR onnxruntime_cxx_api.h
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES include)
find_library(ONNXRUNTIME_LIBRARY onnxruntime
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES lib lib64)

if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIBRARY)
    set(ONNXRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_ONNXRUNTIME)
endif()

# RKNN Runtime (optional, for RKNN models)
find_library(RKNN_API_LIBRARY NAMES rknnrt PATHS /usr/lib)
if(RKNN_API_LIBRARY)
    set(RKNNRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_RKNNRUNTIME)
endif()

# Rockchip RGA (optional, for image processing)
find_library(RGA_LIBRARY NAMES rga)
if(RGA_LIBRARY)
    set(RGA_FOUND TRUE)
    add_definitions(-DHAVE_RGA)
endif()

# Rockchip MPP (optional, for video encoding/decoding)
find_library(RKMPP_LIBRARY NAMES rockchip_mpp)
if(RKMPP_LIBRARY)
    set(RKMPP_FOUND TRUE)
    add_definitions(-DHAVE_RKMPP)
endif()

# Include directories
include_directories(${PROJECT_INCLUDE_DIR})
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${GSTREAMER_INCLUDE_DIRS})
include_directories(${JSONCPP_INCLUDE_DIRS})

if(TENSORRT_FOUND)
    include_directories(${TENSORRT_INCLUDE_DIR})
endif()

if(ONNXRUNTIME_FOUND)
    include_directories(${ONNXRUNTIME_INCLUDE_DIR})
endif()

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})
link_directories(${JSONCPP_LIBRARY_DIRS})

# Include sub CMakeLists.txt files
add_subdirectory(dds_video_frame)

# Common libraries - 重新组织，按依赖顺序
set(COMMON_LIBS)

# 1. 系统基础库 - 最先添加
list(APPEND COMMON_LIBS pthread)
list(APPEND COMMON_LIBS dl)
message(STATUS "Added system libraries: pthread dl")

# 2. 底层库 - GLib/GObject (GStreamer依赖)
# 这些通常由GStreamer自动包含，但确保顺序正确

# 3. FFmpeg库 - 相对独立
if(FFMPEG_LIBRARIES)
    list(APPEND COMMON_LIBS ${FFMPEG_LIBRARIES})
    message(STATUS "Added FFmpeg libraries: ${FFMPEG_LIBRARIES}")
else()
    message(WARNING "FFmpeg libraries not found")
endif()

# 4. JsonCpp - 相对独立
if(JSONCPP_LIBRARIES)
    list(APPEND COMMON_LIBS ${JSONCPP_LIBRARIES})
    message(STATUS "Added JsonCpp libraries: ${JSONCPP_LIBRARIES}")
else()
    message(WARNING "JsonCpp libraries not found")
endif()

# 5. GStreamer库 - 复杂依赖，但通常自包含
if(GSTREAMER_LIBRARIES)
    list(APPEND COMMON_LIBS ${GSTREAMER_LIBRARIES})
    message(STATUS "Added GStreamer libraries: ${GSTREAMER_LIBRARIES}")
else()
    message(WARNING "GStreamer libraries not found")
endif()

# 6. DDS库 - 可能与其他库有冲突，放在后面
list(APPEND COMMON_LIBS DDSVideoFrame_lib_${PROJECT_NAME})
list(APPEND COMMON_LIBS fastdds)
list(APPEND COMMON_LIBS fastcdr)
message(STATUS "Added DDS libraries")

# 8. AI库 - 通常问题较少
if(TENSORRT_FOUND AND TENSORRT_LIBRARY)
    list(APPEND COMMON_LIBS ${TENSORRT_LIBRARY})
    message(STATUS "Added TensorRT library: ${TENSORRT_LIBRARY}")
endif()

if(ONNXRUNTIME_FOUND AND ONNXRUNTIME_LIBRARY)
    list(APPEND COMMON_LIBS ${ONNXRUNTIME_LIBRARY})
    message(STATUS "Added ONNX Runtime library: ${ONNXRUNTIME_LIBRARY}")
endif()

# 创建一个安全的COMMON_LIBS版本（不包含硬件库）
set(SAFE_COMMON_LIBS ${COMMON_LIBS})

message(STATUS "Final SAFE_COMMON_LIBS: ${SAFE_COMMON_LIBS}")

# 创建硬件库列表，供需要时单独添加
set(HARDWARE_LIBS)
if(RGA_LIBRARY AND RGA_FOUND)
    list(APPEND HARDWARE_LIBS ${RGA_LIBRARY})
    message(STATUS "Hardware lib available: RGA - ${RGA_LIBRARY}")
endif()

if(RKNN_API_LIBRARY AND RKNNRUNTIME_FOUND)
    list(APPEND HARDWARE_LIBS ${RKNN_API_LIBRARY})
    message(STATUS "Hardware lib available: RKNN - ${RKNN_API_LIBRARY}")
endif()

if(RKMPP_LIBRARY AND RKMPP_FOUND)
    list(APPEND HARDWARE_LIBS ${RKMPP_LIBRARY})
    message(STATUS "Hardware lib available: MPP - ${RKMPP_LIBRARY}")
endif()

if(HARDWARE_LIBS)
    message(STATUS "Hardware libraries : ${HARDWARE_LIBS}")
    list(APPEND COMMON_LIBS ${HARDWARE_LIBS})
endif()

message(STATUS "Final COMMON_LIBS: ${COMMON_LIBS}")

# Video Capture Service
add_executable(video_capture
    ${PROJECT_SOURCE_DIR}/video_capture_main.cpp
)
target_link_libraries(video_capture ${COMMON_LIBS})

# Video Converter Service
add_executable(video_converter
    ${PROJECT_SOURCE_DIR}/video_converter_main.cpp
)
target_link_libraries(video_converter ${COMMON_LIBS})

# AI Processor Service
add_executable(ai_processor
    ${PROJECT_SOURCE_DIR}/ai_processor_main.cpp
)
target_link_libraries(ai_processor ${COMMON_LIBS})

# Cloud Streamer Service
add_executable(cloud_streamer
    ${PROJECT_SOURCE_DIR}/cloud_streamer_main.cpp
)
target_link_libraries(cloud_streamer ${COMMON_LIBS})

# RTSP Server Service
add_executable(rtsp_server
    ${PROJECT_SOURCE_DIR}/rtsp_server_main.cpp
    ${PROJECT_SOURCE_DIR}/rtsp_server.cpp
)
target_link_libraries(rtsp_server ${COMMON_LIBS})

# RTSP PIPELINE Server
add_executable(rtsp_server_pipe ${PROJECT_SOURCE_DIR}/rtsp_server_pipe.cpp)
target_link_libraries(rtsp_server_pipe ${GSTREAMER_LIBRARIES})

# RTSP PIPELINE Server with DDS
add_executable(rtsp_server_dds ${PROJECT_SOURCE_DIR}/rtsp_server_dds.cpp)
target_link_libraries(rtsp_server_dds ${COMMON_LIBS})

# Video Control Service
add_executable(video_control
    ${PROJECT_SOURCE_DIR}/video_control_main.cpp
    ${PROJECT_SOURCE_DIR}/video_control.cpp
    ${PROJECT_SOURCE_DIR}/rtsp_server.cpp
)
target_link_libraries(video_control ${COMMON_LIBS})

# Testing support
option(BUILD_TESTS "Build tests" OFF)

if(BUILD_TESTS)
    # Find Google Test
    find_package(GTest QUIET)
    if(GTest_FOUND)
        enable_testing()

        # RGA Accelerator tests (standalone)
        add_executable(test_rga_accelerator
            ${PROJECT_TEST_DIR}/test_rga_accelerator.cpp
        )
        target_link_libraries(test_rga_accelerator
            ${COMMON_LIBS}
        )

        # MPP Decoder tests (standalone)
        add_executable(test_mpp_decoder
            ${PROJECT_TEST_DIR}/test_mpp_decoder.cpp
        )
        target_link_libraries(test_mpp_decoder
            ${COMMON_LIBS}
        )

        # MPP Decoder tests (Google Test)
        add_executable(test_mpp_decoder_gtest
            ${PROJECT_TEST_DIR}/test_mpp_decoder_gtest.cpp
        )
        target_link_libraries(test_mpp_decoder_gtest
            ${COMMON_LIBS}
            GTest::gtest
            GTest::gtest_main
            GTest::gmock
        )

        # Video Converter comprehensive tests (standalone)
        add_executable(test_video_converter
            ${PROJECT_TEST_DIR}/test_video_converter.cpp
        )
        target_link_libraries(test_video_converter
            ${COMMON_LIBS}
        )
        
        # Video Control Tests (standalone)
        add_executable(test_video_control
            ${PROJECT_SOURCE_DIR}/video_control.cpp
            ${PROJECT_SOURCE_DIR}/rtsp_server.cpp
            ${PROJECT_TEST_DIR}/test_video_control.cpp
        )
        target_link_libraries(test_video_control
            ${COMMON_LIBS}
        )

        # Install test executables
        install(TARGETS
            test_rga_accelerator
            test_mpp_decoder test_mpp_decoder_gtest test_video_converter test_video_control
            DESTINATION bin)
    else()
        message(WARNING "Google Test not found. Tests will not be built.")
    endif()
endif()

# Install targets
install(TARGETS
    video_capture
    video_converter
    ai_processor
    cloud_streamer
    rtsp_server
    rtsp_server_pipe
    rtsp_server_dds
    video_control
    DESTINATION bin
)

# Install header files
install(DIRECTORY ${PROJECT_INCLUDE_DIR}/
    DESTINATION include/video_service
    FILES_MATCHING PATTERN "*.h"
)

# Install configuration files
install(FILES
    ${PROJECT_CONFIG_DIR}/ai_processor.json
    ${PROJECT_CONFIG_DIR}/cloud_streamer.json
    ${PROJECT_CONFIG_DIR}/rtsp_server.json
    ${PROJECT_CONFIG_DIR}/video_converter.json
    ${PROJECT_CONFIG_DIR}/video_capture.json
    ${PROJECT_CONFIG_DIR}/video_control.json
    DESTINATION share/video_service/config
)

# Install scripts
install(DIRECTORY ${PROJECT_SCRIPTS_DIR}/
    DESTINATION share/video_service/scripts
    FILES_MATCHING PATTERN "*.sh"
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE
                GROUP_READ GROUP_EXECUTE
                WORLD_READ WORLD_EXECUTE
)

# Install documentation
install(FILES
    ${PROJECT_DOCS_DIR}/README.md
    DESTINATION share/doc/video_service
)

# Install systemd service files (if systemd is available)
if(EXISTS "/lib/systemd/system")
    configure_file(
        ${PROJECT_CONFIG_DIR}/video_service.service.in
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        @ONLY
    )
    install(FILES
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        DESTINATION /lib/systemd/system
    )
endif()

# Create package
set(CPACK_PACKAGE_NAME "video-service")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Embedded Linux Video Service System")
set(CPACK_PACKAGE_VENDOR "Video Service Team")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# DEB package specific
set(CPACK_DEBIAN_PACKAGE_DEPENDS
    "libavformat58, libavcodec58, libavutil56, libgstreamer1.0-0 (>= 1.16), libgstreamer-plugins-base1.0-0")
set(CPACK_DEBIAN_PACKAGE_SECTION "video")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

# RPM package specific
set(CPACK_RPM_PACKAGE_REQUIRES
    "ffmpeg-libs >= 4.0, gstreamer1 >= 1.16, gstreamer1-plugins-base")
set(CPACK_RPM_PACKAGE_GROUP "Applications/Multimedia")

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "Video Service Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  FFmpeg found: ${FFMPEG_FOUND}")
message(STATUS "  GStreamer found: ${GSTREAMER_FOUND}")
message(STATUS "  OpenCV found: ${OpenCV_FOUND}")
message(STATUS "  JsonCpp found: ${JSONCPP_FOUND}")
message(STATUS "  Fast DDS found: ${fastdds_FOUND}")
message(STATUS "  Fast CDR found: ${fastcdr_FOUND}")
message(STATUS "  TensorRT found: ${TENSORRT_FOUND}")
message(STATUS "  ONNX Runtime found: ${ONNXRUNTIME_FOUND}")
message(STATUS "  RKNN Runtime found: ${RKNNRUNTIME_FOUND}")
message(STATUS "  Rockchip RGA found: ${RGA_FOUND}")
message(STATUS "  Rockchip MPP found: ${RKMPP_FOUND}")
message(STATUS "")
