#ifndef GSTREAMER_ENCODER_H
#define GSTREAMER_ENCODER_H

#include "common.h"
#include <sstream>

// GStreamer编码器接口
class GStreamerEncoder {
private:
    GstElement* encode_pipeline_ = nullptr;
    GstElement* encode_appsrc_ = nullptr;
    GstElement* encode_appsink_ = nullptr;
    bool gst_initialized_ = false;

public:
    GStreamerEncoder() = default;
    ~GStreamerEncoder() { cleanup(); }

    bool init();
    bool encode_h264(const Frame& src, Frame& dst, int bitrate = 2000000);
    bool encode_h265(const Frame& src, Frame& dst, int bitrate = 2000000);
    bool encode_jpeg(const Frame& src, Frame& dst, int quality = 85);
    void cleanup();

private:
    bool init_encode_pipeline(const std::string& encoder);
    bool push_frame_to_pipeline(GstElement* appsrc, const Frame& frame);
    bool pull_frame_from_pipeline(GstElement* appsink, Frame& frame);
    std::string create_encode_pipeline_desc(const std::string& encoder, int bitrate);
    std::string create_jpeg_pipeline_desc(int quality);
};

// GStreamerEncoder实现
inline bool GStreamerEncoder::init() {
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    gst_initialized_ = true;
    LOG_I("GStreamer encoder initialized");
    return true;
}

inline bool GStreamerEncoder::encode_h264(const Frame& src, Frame& dst, int bitrate) {
    if (!gst_initialized_) {
        return false;
    }

    // 创建编码pipeline
    std::string pipeline_desc = create_encode_pipeline_desc("mpph264enc", bitrate);

    GError* error = nullptr;
    GstElement* pipeline = gst_parse_launch(pipeline_desc.c_str(), &error);
    if (!pipeline || error) {
        LOG_E("Failed to create H264 encode pipeline: %s", error ? error->message : "unknown");
        if (error) g_error_free(error);
        return false;
    }

    // 获取appsrc和appsink
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    GstElement* appsink = gst_bin_get_by_name(GST_BIN(pipeline), "sink");

    if (!appsrc || !appsink) {
        LOG_E("Failed to get appsrc or appsink elements");
        gst_object_unref(pipeline);
        return false;
    }

    // 启动pipeline
    gst_element_set_state(pipeline, GST_STATE_PLAYING);

    // 推送数据
    bool success = push_frame_to_pipeline(appsrc, src);
    if (success) {
        success = pull_frame_from_pipeline(appsink, dst);
    }

    // 清理
    gst_element_set_state(pipeline, GST_STATE_NULL);
    gst_object_unref(appsrc);
    gst_object_unref(appsink);
    gst_object_unref(pipeline);

    return success;
}

inline bool GStreamerEncoder::encode_h265(const Frame& src, Frame& dst, int bitrate) {
    if (!gst_initialized_) {
        return false;
    }

    // 创建编码pipeline
    std::string pipeline_desc = create_encode_pipeline_desc("mpph265enc", bitrate);

    GError* error = nullptr;
    GstElement* pipeline = gst_parse_launch(pipeline_desc.c_str(), &error);
    if (!pipeline || error) {
        LOG_E("Failed to create H265 encode pipeline: %s", error ? error->message : "unknown");
        if (error) g_error_free(error);
        return false;
    }

    // 获取appsrc和appsink
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    GstElement* appsink = gst_bin_get_by_name(GST_BIN(pipeline), "sink");

    if (!appsrc || !appsink) {
        LOG_E("Failed to get appsrc or appsink elements");
        gst_object_unref(pipeline);
        return false;
    }

    // 启动pipeline
    gst_element_set_state(pipeline, GST_STATE_PLAYING);

    // 推送数据
    bool success = push_frame_to_pipeline(appsrc, src);
    if (success) {
        success = pull_frame_from_pipeline(appsink, dst);
    }

    // 清理
    gst_element_set_state(pipeline, GST_STATE_NULL);
    gst_object_unref(appsrc);
    gst_object_unref(appsink);
    gst_object_unref(pipeline);

    return success;
}

inline bool GStreamerEncoder::encode_jpeg(const Frame& src, Frame& dst, int quality) {
    if (!gst_initialized_) {
        return false;
    }

    // 创建JPEG编码pipeline
    std::string pipeline_desc = create_jpeg_pipeline_desc(quality);

    GError* error = nullptr;
    GstElement* pipeline = gst_parse_launch(pipeline_desc.c_str(), &error);
    if (!pipeline || error) {
        LOG_E("Failed to create JPEG encode pipeline: %s", error ? error->message : "unknown");
        if (error) g_error_free(error);
        return false;
    }

    // 获取appsrc和appsink
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    GstElement* appsink = gst_bin_get_by_name(GST_BIN(pipeline), "sink");

    if (!appsrc || !appsink) {
        LOG_E("Failed to get appsrc or appsink elements");
        gst_object_unref(pipeline);
        return false;
    }

    // 启动pipeline
    gst_element_set_state(pipeline, GST_STATE_PLAYING);

    // 推送数据
    bool success = push_frame_to_pipeline(appsrc, src);
    if (success) {
        success = pull_frame_from_pipeline(appsink, dst);
    }

    // 清理
    gst_element_set_state(pipeline, GST_STATE_NULL);
    gst_object_unref(appsrc);
    gst_object_unref(appsink);
    gst_object_unref(pipeline);

    return success;
}

inline void GStreamerEncoder::cleanup() {
    if (encode_pipeline_) {
        gst_element_set_state(encode_pipeline_, GST_STATE_NULL);
        gst_object_unref(encode_pipeline_);
        encode_pipeline_ = nullptr;
    }

    if (encode_appsrc_) {
        gst_object_unref(encode_appsrc_);
        encode_appsrc_ = nullptr;
    }

    if (encode_appsink_) {
        gst_object_unref(encode_appsink_);
        encode_appsink_ = nullptr;
    }
}

inline std::string GStreamerEncoder::create_encode_pipeline_desc(const std::string& encoder, int bitrate) {
    std::ostringstream pipeline;

    pipeline << "appsrc name=source ! queue ! ";

    if (encoder == "mpph264enc") {
        pipeline << "mpph264enc bps=" << bitrate
                 << " bps-min=" << (bitrate/2)
                 << " bps-max=" << (bitrate*2)
                 << " profile=baseline gop=15 rc-mode=1 ! ";
    } else if (encoder == "mpph265enc") {
        pipeline << "mpph265enc bps=" << bitrate
                 << " bps-min=" << (bitrate/2)
                 << " bps-max=" << (bitrate*2)
                 << " profile=main gop=15 rc-mode=1 ! ";
    } else {
        // 不支持的编码器，返回错误
        LOG_E("Unsupported encoder: %s. Only hardware encoders (mpph264enc/mpph265enc) are supported", encoder.c_str());
        return "";
    }

    pipeline << "appsink name=sink";

    return pipeline.str();
}

inline std::string GStreamerEncoder::create_jpeg_pipeline_desc(int quality) {
    std::ostringstream pipeline;

    pipeline << "appsrc name=source ! queue ! "
             << "mppjpegenc quality=" << quality << " ! "
             << "appsink name=sink";

    return pipeline.str();
}

inline bool GStreamerEncoder::push_frame_to_pipeline(GstElement* appsrc, const Frame& frame) {
    if (!appsrc || frame.data.empty()) {
        return false;
    }

    GstBuffer* buffer = gst_buffer_new_allocate(nullptr, frame.data.size(), nullptr);
    if (!buffer) {
        LOG_E("Failed to allocate GstBuffer");
        return false;
    }

    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
        LOG_E("Failed to map GstBuffer");
        gst_buffer_unref(buffer);
        return false;
    }

    memcpy(map.data, frame.data.data(), frame.data.size());
    gst_buffer_unmap(buffer, &map);

    GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc), buffer);
    if (ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", ret);
        return false;
    }

    return true;
}

inline bool GStreamerEncoder::pull_frame_from_pipeline(GstElement* appsink, Frame& frame) {
    if (!appsink) {
        return false;
    }

    GstSample* sample = gst_app_sink_pull_sample(GST_APP_SINK(appsink));
    if (!sample) {
        LOG_E("Failed to pull sample from appsink");
        return false;
    }

    GstBuffer* buffer = gst_sample_get_buffer(sample);
    if (!buffer) {
        LOG_E("Failed to get buffer from sample");
        gst_sample_unref(sample);
        return false;
    }

    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_READ)) {
        LOG_E("Failed to map buffer");
        gst_sample_unref(sample);
        return false;
    }

    // 复制数据到Frame
    frame.data.resize(map.size);
    memcpy(frame.data.data(), map.data, map.size);

    gst_buffer_unmap(buffer, &map);
    gst_sample_unref(sample);

    return true;
}

#endif // GSTREAMER_ENCODER_H
