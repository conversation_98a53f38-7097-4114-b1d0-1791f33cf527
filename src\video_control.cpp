#include "video_control.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <cstring>
#include <random>
#include <iomanip>
#include "rtsp_server.h"

VideoControlService::VideoControlService()
    : visible_frame_queue_(10), infrared_frame_queue_(10) {
    // Initialize GStreamer
    RTSPServerUtils::init_gstreamer();
}

VideoControlService::~VideoControlService() {
    stop();
}

bool VideoControlService::init(const VideoControlConfig& config) {
    config_ = config;

    if (config_.log_level == "DEBUG") {
        Logger::set_level(LEVEL_DEBUG);
        RTSPServerUtils::set_gstreamer_debug_level(5);
    } else if (config_.log_level == "INFO") {
        Logger::set_level(LEVEL_INFO);
        RTSPServerUtils::set_gstreamer_debug_level(4);        
    } else if (config_.log_level == "WARN") {
        Logger::set_level(LEVEL_WARN);
        RTSPServerUtils::set_gstreamer_debug_level(3);
    } else {
        Logger::set_level(LEVEL_ERROR);
        RTSPServerUtils::set_gstreamer_debug_level(2);
    }
    LOG_I("Initializing Video Control Service...");
    
    // Initialize UDP socket
    if (!init_udp_socket()) {
        LOG_E("Failed to initialize UDP socket");
        return false;
    }
    
    // Initialize dual DDS readers
    try {
        visible_dds_reader_ = std::make_unique<DDSVideoReader>(
            config_.visible_stream.dds_topic, config_.max_samples);
        LOG_I("Visible DDS reader initialized for topic: %s",
              config_.visible_stream.dds_topic.c_str());

        infrared_dds_reader_ = std::make_unique<DDSVideoReader>(
            config_.infrared_stream.dds_topic, config_.max_samples);
        LOG_I("Infrared DDS reader initialized for topic: %s",
              config_.infrared_stream.dds_topic.c_str());
    } catch (const std::exception& e) {
        LOG_E("Failed to initialize DDS readers: %s", e.what());
        return false;
    }

    // Initialize JPEG encoders for photo capture
    try {
        visible_jpeg_encoder_ = std::make_unique<GStreamerEncoder>(
            EncoderType::JPEG, config_.visible_stream.jpeg_quality);
        if (!visible_jpeg_encoder_->init()) {
            LOG_E("Failed to initialize visible JPEG encoder");
            return false;
        }

        infrared_jpeg_encoder_ = std::make_unique<GStreamerEncoder>(
            EncoderType::JPEG, config_.infrared_stream.jpeg_quality);
        if (!infrared_jpeg_encoder_->init()) {
            LOG_E("Failed to initialize infrared JPEG encoder");
            return false;
        }

        LOG_I("JPEG encoders initialized for dual stream photo capture");
    } catch (const std::exception& e) {
        LOG_E("Failed to initialize JPEG encoders: %s", e.what());
        return false;
    }
    
    // Ensure directories exist
    if (!ensure_directories_exist()) {
        LOG_E("Failed to create required directories");
        return false;
    }
    
    // Initialize SD card status
    check_sdcard_status();
    
    LOG_I("Video Control Service initialized successfully");
    return true;
}

bool VideoControlService::start() {
    if (running_.load()) {
        LOG_W("Service is already running");
        return true;
    }
    
    LOG_I("Starting Video Control Service...");
    
    stop_requested_.store(false);
    running_.store(true);
    
    // Start threads
    udp_receiver_thread_ = std::thread(&VideoControlService::udp_receiver_loop, this);
    status_reporter_thread_ = std::thread(&VideoControlService::status_reporter_loop, this);
    frame_processor_thread_ = std::thread(&VideoControlService::visible_frame_processor_loop, this);

    // Start infrared frame processor thread
    std::thread infrared_processor_thread(&VideoControlService::infrared_frame_processor_loop, this);
    infrared_processor_thread.detach(); // Let it run independently
    
    LOG_I("Video Control Service started successfully");
    return true;
}

void VideoControlService::stop() {
    if (!running_.load()) {
        return;
    }
    
    LOG_I("Stopping Video Control Service...");

    // Set stop flag first
    stop_requested_.store(true);
    running_.store(false);

    // Stop recording if active
    if (recording_session_.is_active) {
        stop_recording_session();
    }

    // Close UDP socket to interrupt select() call
    if (udp_socket_fd_ >= 0) {
        close(udp_socket_fd_);
        udp_socket_fd_ = -1;
    }

    // Give threads a moment to see the stop flag and socket closure
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // Join threads
    if (udp_receiver_thread_.joinable()) {
        udp_receiver_thread_.join();
    }
    if (status_reporter_thread_.joinable()) {
        status_reporter_thread_.join();
    }
    if (frame_processor_thread_.joinable()) {
        frame_processor_thread_.join();
    }
    
    LOG_I("Video Control Service stopped");
}

void VideoControlService::handle_signal(int signal) {
    switch (signal) {
        case SIGUSR1:
            LOG_I("Received SIGUSR1 - Triggering status report");
            check_sdcard_status();
            break;
        case SIGUSR2:
            LOG_I("Received SIGUSR2 - Stopping recording if active");
            if (recording_session_.is_active) {
                stop_recording_session();
            }
            break;
        default:
            break;
    }
}

void VideoControlService::get_stats(Stats& stats) const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats = stats_;
    stats.cpu_usage = const_cast<CPUMonitor&>(cpu_monitor_).get_usage();
    
    // Get memory usage
    std::ifstream status_file("/proc/self/status");
    std::string line;
    while (std::getline(status_file, line)) {
        if (line.find("VmRSS:") == 0) {
            std::istringstream iss(line);
            std::string label, value, unit;
            iss >> label >> value >> unit;
            stats.memory_usage_mb = std::stof(value) / 1024.0f; // Convert KB to MB
            break;
        }
    }
}

SDCardStatus VideoControlService::get_sdcard_status() const {
    std::lock_guard<std::mutex> lock(sdcard_mutex_);
    return sdcard_status_;
}

bool VideoControlService::init_udp_socket() {
    udp_socket_fd_ = socket(AF_INET, SOCK_DGRAM, 0);
    if (udp_socket_fd_ < 0) {
        LOG_E("Failed to create UDP socket: %s", strerror(errno));
        return false;
    }
    
    // Set socket options
    int opt = 1;
    if (setsockopt(udp_socket_fd_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        LOG_W("Failed to set SO_REUSEADDR: %s", strerror(errno));
    }
    
    // Keep socket in blocking mode (default)
    // No need to set O_NONBLOCK since we want blocking receive
    
    // Bind socket
    memset(&udp_addr_, 0, sizeof(udp_addr_));
    udp_addr_.sin_family = AF_INET;
    udp_addr_.sin_port = htons(config_.udp_port);
    
    if (inet_pton(AF_INET, config_.udp_bind_ip.c_str(), &udp_addr_.sin_addr) <= 0) {
        LOG_E("Invalid bind IP address: %s", config_.udp_bind_ip.c_str());
        close(udp_socket_fd_);
        udp_socket_fd_ = -1;
        return false;
    }
    
    if (bind(udp_socket_fd_, (struct sockaddr*)&udp_addr_, sizeof(udp_addr_)) < 0) {
        LOG_E("Failed to bind UDP socket to %s:%d: %s", 
              config_.udp_bind_ip.c_str(), config_.udp_port, strerror(errno));
        close(udp_socket_fd_);
        udp_socket_fd_ = -1;
        return false;
    }
    
    LOG_I("UDP socket bound to %s:%d", config_.udp_bind_ip.c_str(), config_.udp_port);
    return true;
}

void VideoControlService::udp_receiver_loop() {
    LOG_I("UDP receiver thread started");
    
    uint8_t buffer[1024];
    struct sockaddr_in client_addr;
    socklen_t client_len = sizeof(client_addr);
    
    while (!stop_requested_.load()) {
        // Check if socket is still valid
        if (udp_socket_fd_ < 0) {
            LOG_W("UDP socket closed, exiting receiver loop");
            break;
        }

        // Use blocking receive with timeout
        struct timeval timeout;
        timeout.tv_sec = 1;  // 1 second timeout
        timeout.tv_usec = 0;

        if (setsockopt(udp_socket_fd_, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
            LOG_E("Failed to set socket timeout: %s", strerror(errno));
            break;
        }

        ssize_t bytes_received = recvfrom(udp_socket_fd_, buffer, sizeof(buffer), 0,
                                        (struct sockaddr*)&client_addr, &client_len);

        if (bytes_received > 0) {
            {
                std::lock_guard<std::mutex> lock(stats_mutex_);
                stats_.mavlink_messages_received++;
            }

            // Parse and process mavlink message
            MavlinkMessage message;
            if (parse_mavlink_message(buffer, bytes_received, message)) {
                process_mavlink_message(message);

                std::lock_guard<std::mutex> lock(stats_mutex_);
                stats_.mavlink_messages_processed++;
            } else {
                LOG_W("Failed to parse mavlink message");
            }
        } else if (bytes_received < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // Timeout, continue loop to check stop_requested
                continue;
            } else if (errno == EBADF || errno == EINVAL) {
                LOG_W("UDP socket closed or invalid, exiting receiver loop");
                break;
            } else {
                LOG_E("UDP receive error: %s", strerror(errno));
                // Continue on other errors
            }
        }
    }

    // Close UDP socket on exit
    if (udp_socket_fd_ >= 0) {
        close(udp_socket_fd_);
        udp_socket_fd_ = -1;
    }
    
    LOG_I("UDP receiver thread stopped");
}

// Placeholder implementation for mavlink parsing
bool VideoControlService::parse_mavlink_message(const uint8_t* data, size_t length, MavlinkMessage& message) {
    // TODO: Implement actual mavlink parsing when library is integrated
    // For now, create a simple placeholder implementation
    
    if (length < 4) {
        return false;
    }
    
    // Simple parsing - first byte determines command type
    switch (data[0]) {
        case 1:
            message.type = MAV_CMD_TAKE_PHOTO;
            break;
        case 2:
            message.type = MAV_CMD_START_RECORDING;
            break;
        case 3:
            message.type = MAV_CMD_STOP_RECORDING;
            break;
        case 4:
            message.type = MAV_CMD_GET_STATUS;
            break;
        default:
            return false;
    }
    
    message.sequence_id = (length > 1) ? data[1] : 0;
    message.timestamp = get_current_us();
    message.payload.assign(data + 2, data + length);

    return true;
}

void VideoControlService::process_mavlink_message(const MavlinkMessage& message) {
    LOG_D("Processing mavlink message type: %d, seq: %u", message.type, message.sequence_id);

    bool success = false;
    std::string response_msg;

    switch (message.type) {
        case MAV_CMD_TAKE_PHOTO: {
            // Capture photos from both streams
            PhotoResult visible_result, infrared_result;
            capture_photo_dual(visible_result, infrared_result);
            success = visible_result.success && infrared_result.success;
            response_msg = visible_result.success ? visible_result.photo_path : visible_result.error_message;

            std::lock_guard<std::mutex> lock(stats_mutex_);
            if (visible_result.success) {
                stats_.photos_taken++;
            } else {
                stats_.photos_failed++;
            }
            break;
        }

        case MAV_CMD_START_RECORDING: {
            if (!recording_session_.is_active) {
                success = start_recording_session();
                response_msg = success ? "Recording started" : "Failed to start recording";
            } else {
                success = true;
                response_msg = "Recording already active";
            }
            break;
        }

        case MAV_CMD_STOP_RECORDING: {
            if (recording_session_.is_active) {
                success = stop_recording_session();
                response_msg = success ? "Recording stopped" : "Failed to stop recording";
            } else {
                success = true;
                response_msg = "Recording not active";
            }
            break;
        }

        case MAV_CMD_GET_STATUS: {
            // Return current status
            SDCardStatus status = get_sdcard_status();
            std::ostringstream oss;
            oss << "SD:" << (status.is_mounted ? "OK" : "NO")
                << " Space:" << status.available_capacity_mb << "MB"
                << " Recording:" << (status.is_recording ? "YES" : "NO");
            response_msg = oss.str();
            success = true;
            break;
        }

        default:
            response_msg = "Unknown command";
            break;
    }

    // Send response back to client
    send_mavlink_response(message, success, response_msg);
}

void VideoControlService::send_mavlink_response(const MavlinkMessage& request, bool success, const std::string& message) {
    // TODO: Implement actual mavlink response when library is integrated
    // For now, just log the response
    LOG_I("Mavlink response - Seq: %u, Success: %s, Message: %s",
          request.sequence_id, success ? "YES" : "NO", message.c_str());
}

void VideoControlService::status_reporter_loop() {
    LOG_I("Status reporter thread started");

    while (!stop_requested_.load()) {
        check_sdcard_status();

        // Sleep for configured interval
        for (int i = 0; i < config_.status_report_interval_sec && !stop_requested_.load(); ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    LOG_I("Status reporter thread stopped");
}

bool VideoControlService::check_sdcard_status() {
    std::lock_guard<std::mutex> lock(sdcard_mutex_);

    // Check if SD card is mounted
    sdcard_status_.is_mounted = is_sdcard_mounted(config_.sdcard_mount_path);
    sdcard_status_.mount_point = config_.sdcard_mount_path;

    if (sdcard_status_.is_mounted) {
        sdcard_status_.is_inserted = true;

        // Get filesystem information
        struct statvfs stat;
        if (statvfs(config_.sdcard_mount_path.c_str(), &stat) == 0) {
            uint64_t total_bytes = stat.f_blocks * stat.f_frsize;
            uint64_t available_bytes = stat.f_bavail * stat.f_frsize;
            uint64_t used_bytes = total_bytes - available_bytes;

            sdcard_status_.total_capacity_mb = total_bytes / (1024 * 1024);
            sdcard_status_.available_capacity_mb = available_bytes / (1024 * 1024);
            sdcard_status_.used_capacity_mb = used_bytes / (1024 * 1024);

            if (total_bytes > 0) {
                sdcard_status_.usage_percentage = (float)used_bytes / total_bytes * 100.0f;
            }
        }
    } else {
        sdcard_status_.is_inserted = false;
        sdcard_status_.total_capacity_mb = 0;
        sdcard_status_.available_capacity_mb = 0;
        sdcard_status_.used_capacity_mb = 0;
        sdcard_status_.usage_percentage = 0.0f;
    }

    // Update recording status
    sdcard_status_.is_recording = recording_session_.is_active;
    if (recording_session_.is_active) {
        uint64_t current_time = get_current_us();
        sdcard_status_.recording_duration_sec = (current_time - recording_session_.start_timestamp) / 1000000;
        sdcard_status_.current_visible_file = recording_session_.visible_session.current_file_path;
        sdcard_status_.current_infrared_file = recording_session_.infrared_session.current_file_path;
        sdcard_status_.current_visible_size_mb = recording_session_.visible_session.stream_size_bytes / (1024 * 1024);
        sdcard_status_.current_infrared_size_mb = recording_session_.infrared_session.stream_size_bytes / (1024 * 1024);
        sdcard_status_.total_visible_segments = recording_session_.visible_session.segment_files.size();
        sdcard_status_.total_infrared_segments = recording_session_.infrared_session.segment_files.size();
    } else {
        sdcard_status_.recording_duration_sec = 0;
        sdcard_status_.current_visible_file.clear();
        sdcard_status_.current_infrared_file.clear();
    }

    return sdcard_status_.is_mounted;
}

bool VideoControlService::ensure_directories_exist() {
    bool success = true;

    success &= create_directory_if_not_exists(config_.photo_save_path);
    success &= create_directory_if_not_exists(config_.video_save_path);

    if (success) {
        LOG_I("Required directories created/verified");
    } else {
        LOG_E("Failed to create required directories");
    }

    return success;
}

bool VideoControlService::is_sdcard_mounted(const std::string& mount_path) {
    struct stat stat_buf;
    if (stat(mount_path.c_str(), &stat_buf) != 0) {
        return false;
    }

    // Check if it's a directory
    if (!S_ISDIR(stat_buf.st_mode)) {
        return false;
    }

    // Check if it's a mount point by comparing device IDs
    std::string parent_path = mount_path + "/..";
    struct stat parent_stat;
    if (stat(parent_path.c_str(), &parent_stat) != 0) {
        return false;
    }

    // If device IDs are different, it's likely a mount point
    return stat_buf.st_dev != parent_stat.st_dev;
}

bool VideoControlService::capture_photo_dual(PhotoResult &visible_result, PhotoResult &infrared_result) {

    if (!capture_visible_photo(visible_result)) {
        return false;
    }

    if (!capture_infrared_photo(infrared_result)) {
        return false;
    }

    return true;
}

bool VideoControlService::capture_visible_photo(PhotoResult & result) {
    Frame visible_frame;
    if (!visible_dds_reader_->read(visible_frame, 100)) { // 100ms timeout
        LOG_E("No visible frame available");
        result.error_message = "No visible frame available";
        return false;
    }

    return capture_single_photo(result, visible_frame, "visible", visible_jpeg_encoder_.get());
}

bool VideoControlService::capture_infrared_photo(PhotoResult & result) {
    Frame infrared_frame;
    if (!infrared_dds_reader_->read(infrared_frame, 100)) { // 100ms timeout
        LOG_E("No infrared frame available");
        result.error_message = "No infrared frame available";
        return false;
    }

    return capture_single_photo(result, infrared_frame, "infrared", infrared_jpeg_encoder_.get());
}

bool VideoControlService::capture_single_photo(PhotoResult & result, const Frame& frame, const std::string& stream_name,
                                    GStreamerEncoder* encoder) {
    if (!frame.valid) {
        result.error_message = "Invalid " + stream_name + " frame";
        return false;
    }

    if (!sdcard_status_.is_mounted) {
        result.error_message = "SD card not available";
        return false;
    }

    if (sdcard_status_.available_capacity_mb < 10) {
        result.error_message = "Low disk space";
        return false;
    }

    // Generate photo filename
    std::string photo_path = generate_photo_filename(stream_name);

    LOG_D("Capturing %s photo: %s", stream_name.c_str(), photo_path.c_str());

    // Save frame as JPEG using GStreamer encoder
    if (save_frame_as_jpeg(frame, photo_path, encoder)) {
        result.success = true;
        result.photo_path = photo_path;
        result.capture_timestamp = frame.timestamp;

        // Get file size
        struct stat stat_buf;
        if (stat(photo_path.c_str(), &stat_buf) == 0) {
            result.photo_size_bytes = stat_buf.st_size;
        }

        LOG_D("Photo captured: %s (%lu bytes)",
              photo_path.c_str(), result.photo_size_bytes);
    } else {
        result.error_message = "Failed to save " + stream_name + " photo";
    }

    return true;
}

std::string VideoControlService::generate_photo_filename(const std::string& stream_name) const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::ostringstream oss;
    oss << config_.photo_save_path << "/photo_" << stream_name << "_"
        << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
        << "_" << std::setfill('0') << std::setw(3) << ms.count()
        << ".jpg";

    return oss.str();
}

bool VideoControlService::save_frame_as_jpeg(const Frame& frame, const std::string& filepath,
                                            GStreamerEncoder* encoder) {
    if (frame.data.empty()) {
        LOG_E("Frame data is empty");
        return false;
    }

    if (!encoder) {
        LOG_E("JPEG encoder is null");
        return false;
    }

    // Encode frame to JPEG using GStreamer
    Frame encoded_frame;
    if (!encoder->encode(frame, encoded_frame)) {
        LOG_E("Failed to encode frame to JPEG");
        return false;
    }

    // Save encoded JPEG data to file
    std::ofstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        LOG_E("Failed to create photo file: %s", filepath.c_str());
        return false;
    }

    file.write(reinterpret_cast<const char*>(encoded_frame.data.data()),
               encoded_frame.data.size());
    file.close();

    return true;
}

void VideoControlService::visible_frame_processor_loop() {
    LOG_I("Visible frame processor thread started");

    while (!stop_requested_.load()) {
        // Only read frames when recording is active
        if (recording_session_.is_active) {
            Frame frame;
            if (visible_dds_reader_->read(frame, 100)) { // 100ms timeout
                process_frame_for_recording(frame, recording_session_.visible_session,
                                          config_.visible_stream);
            }
        } else {
            // Sleep when not recording to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    LOG_I("Visible frame processor thread stopped");
}

void VideoControlService::infrared_frame_processor_loop() {
    LOG_I("Infrared frame processor thread started");

    while (!stop_requested_.load()) {
        // Only read frames when recording is active
        if (recording_session_.is_active) {
            Frame frame;
            if (infrared_dds_reader_->read(frame, 100)) { // 100ms timeout
                process_frame_for_recording(frame, recording_session_.infrared_session,
                                          config_.infrared_stream);
            }
        } else {
            // Sleep when not recording to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    LOG_I("Infrared frame processor thread stopped");
}

bool VideoControlService::start_recording_session() {
    if (recording_session_.is_active) {
        LOG_W("Recording session already active");
        return true;
    }

    // Check SD card availability
    if (!sdcard_status_.is_mounted) {
        LOG_E("Cannot start recording: SD card not available");
        return false;
    }

    // Initialize recording session
    recording_session_.session_id = generate_session_id();
    recording_session_.start_timestamp = get_current_us();
    recording_session_.current_segment_start = recording_session_.start_timestamp;
    recording_session_.current_segment_index = 0;
    recording_session_.total_duration_sec = 0;
    recording_session_.total_size_bytes = 0;

    // Initialize stream sessions
    recording_session_.visible_session.stream_name = "visible";
    recording_session_.infrared_session.stream_name = "infrared";

    // Create first video segments for both streams
    if (!create_new_video_segment()) {
        LOG_E("Failed to create first video segments");
        recording_session_.reset();
        return false;
    }

    recording_session_.is_active = true;

    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.recording_sessions++;
    }

    LOG_I("Dual recording session started: %s", recording_session_.session_id.c_str());
    return true;
}

bool VideoControlService::stop_recording_session() {
    if (!recording_session_.is_active) {
        LOG_W("No active recording session");
        return true;
    }

    // Cleanup GStreamer pipelines for both streams
    cleanup_stream_gstreamer_pipeline(recording_session_.visible_session);
    cleanup_stream_gstreamer_pipeline(recording_session_.infrared_session);

    // Update statistics
    uint64_t current_time = get_current_us();
    recording_session_.total_duration_sec = (current_time - recording_session_.start_timestamp) / 1000000;
    recording_session_.total_size_bytes = recording_session_.visible_session.stream_size_bytes +
                                         recording_session_.infrared_session.stream_size_bytes;

    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_recording_duration_sec += recording_session_.total_duration_sec;
        stats_.total_data_written_mb += recording_session_.total_size_bytes / (1024 * 1024);
    }

    LOG_I("Dual recording session stopped: %s (duration: %lu sec, visible segments: %lu, infrared segments: %lu)",
          recording_session_.session_id.c_str(),
          recording_session_.total_duration_sec,
          recording_session_.visible_session.segment_files.size(),
          recording_session_.infrared_session.segment_files.size());

    recording_session_.reset();
    return true;
}

bool VideoControlService::create_new_video_segment() {
    // Create segments for both streams
    bool visible_success = create_stream_video_segment(recording_session_.visible_session,
                                                      config_.visible_stream);
    bool infrared_success = create_stream_video_segment(recording_session_.infrared_session,
                                                       config_.infrared_stream);

    if (visible_success && infrared_success) {
        recording_session_.current_segment_start = get_current_us();

        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.video_segments_created += 2; // Two segments created
        }

        LOG_I("Created dual video segments %d - Visible: %s, Infrared: %s",
              recording_session_.current_segment_index,
              recording_session_.visible_session.current_file_path.c_str(),
              recording_session_.infrared_session.current_file_path.c_str());
        return true;
    } else {
        LOG_E("Failed to create video segments - Visible: %s, Infrared: %s",
              visible_success ? "OK" : "FAILED",
              infrared_success ? "OK" : "FAILED");
        return false;
    }
}

bool VideoControlService::create_stream_video_segment(StreamRecordingSession& session,
                                                     const VideoStreamConfig& config) {
    // Generate filename for new segment
    std::string segment_path = generate_video_filename(session.stream_name,
                                                      recording_session_.current_segment_index);
    session.current_file_path = segment_path;

    // Initialize GStreamer pipeline for this segment
    if (!init_stream_gstreamer_pipeline(session, config)) {
        LOG_E("Failed to initialize %s GStreamer pipeline for segment %d",
              session.stream_name.c_str(), recording_session_.current_segment_index);
        return false;
    }

    session.segment_files.push_back(segment_path);

    LOG_I("Created %s video segment %d: %s",
          session.stream_name.c_str(), recording_session_.current_segment_index,
          segment_path.c_str());
    return true;
}

bool VideoControlService::process_frame_for_recording(const Frame& frame,
                                                    StreamRecordingSession& session,
                                                    const VideoStreamConfig& config) {
    if (!recording_session_.is_active || !session.pipeline) {
        return false;
    }

    // Check if we need to create a new segment (5-minute segments)
    uint64_t current_time = get_current_us();
    uint64_t segment_duration_us = (uint64_t)config_.video_segment_duration_min * 60 * 1000000;

    if (current_time - recording_session_.current_segment_start >= segment_duration_us) {
        // Finish current segment and start new one
        cleanup_stream_gstreamer_pipeline(session);
        recording_session_.current_segment_index++;

        if (!create_stream_video_segment(session, config)) {
            LOG_E("Failed to create new %s video segment", session.stream_name.c_str());
            return false;
        }
    }

    // Push frame to GStreamer pipeline
    return push_frame_to_stream_pipeline(frame, session);
}

std::string VideoControlService::generate_video_filename(const std::string& stream_name,
                                                       int segment_index) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << config_.video_save_path << "/video_" << stream_name << "_"
        << recording_session_.session_id << "_"
        << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
        << "_seg" << std::setfill('0') << std::setw(3) << segment_index
        << ".mp4";

    return oss.str();
}

std::string VideoControlService::generate_session_id() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

    return oss.str();
}

bool VideoControlService::init_stream_gstreamer_pipeline(StreamRecordingSession& session,
                                                        const VideoStreamConfig& config) {
    // Create GStreamer pipeline for video encoding
    std::ostringstream pipeline_desc;

    pipeline_desc << "appsrc name=src ! ";

    // Add video format conversion based on input format
    pipeline_desc << "videoconvert ! ";

    // Add video scaling
    pipeline_desc << "videoscale ! "
                 << "video/x-raw,width=" << config.width
                 << ",height=" << config.height << " ! ";

    // Add encoder based on codec configuration
    if (config.codec == "H265") {
        pipeline_desc << "mpph265enc bps=" << config.bitrate
                     << " bps-min=" << (config.bitrate/2)
                     << " bps-max=" << (config.bitrate*2)
                     << " profile=main gop=15 rc-mode=1 ! ";
        pipeline_desc << "h265parse ! ";
    } else {
        pipeline_desc << "mpph264enc bps=" << config.bitrate
                     << " bps-min=" << (config.bitrate/2)
                     << " bps-max=" << (config.bitrate*2)
                     << " profile=baseline gop=15 rc-mode=1 ! ";
        pipeline_desc << "h264parse ! ";
    }

    // Add muxer and file sink
    pipeline_desc << "mp4mux ! "
                  << "filesink location=" << session.current_file_path;

    // Create pipeline
    GError* error = nullptr;
    session.pipeline = gst_parse_launch(pipeline_desc.str().c_str(), &error);

    if (!session.pipeline || error) {
        LOG_E("Failed to create %s GStreamer pipeline: %s",
              session.stream_name.c_str(),
              error ? error->message : "Unknown error");
        if (error) g_error_free(error);
        return false;
    }

    // Get appsrc element
    session.appsrc = gst_bin_get_by_name(GST_BIN(session.pipeline), "src");
    if (!session.appsrc) {
        LOG_E("Failed to get appsrc element for %s", session.stream_name.c_str());
        gst_object_unref(session.pipeline);
        session.pipeline = nullptr;
        return false;
    }

    // Configure appsrc
    g_object_set(session.appsrc,
                 "caps", gst_caps_new_simple("video/x-raw",
                                           "format", G_TYPE_STRING, "I420",
                                           "width", G_TYPE_INT, config.width,
                                           "height", G_TYPE_INT, config.height,
                                           "framerate", GST_TYPE_FRACTION, config.fps, 1,
                                           nullptr),
                 "stream-type", 0, // GST_APP_STREAM_TYPE_STREAM
                 "format", GST_FORMAT_TIME,
                 nullptr);

    // Set up bus for error handling
    GstBus* bus = gst_element_get_bus(session.pipeline);
    gst_bus_add_signal_watch(bus);
    g_signal_connect(bus, "message::error", G_CALLBACK(on_error_message), this);
    g_signal_connect(bus, "message::eos", G_CALLBACK(on_eos_message), this);
    gst_object_unref(bus);

    // Start pipeline
    GstStateChangeReturn ret = gst_element_set_state(session.pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("Failed to start %s GStreamer pipeline", session.stream_name.c_str());
        cleanup_stream_gstreamer_pipeline(session);
        return false;
    }

    LOG_I("%s GStreamer pipeline initialized for %s encoding (%dx%d@%dfps, %dkbps)",
          session.stream_name.c_str(), config.codec.c_str(),
          config.width, config.height, config.fps, config.bitrate/1000);
    return true;
}

void VideoControlService::cleanup_stream_gstreamer_pipeline(StreamRecordingSession& session) {
    if (session.pipeline) {
        // Send EOS to finish the file properly
        if (session.appsrc) {
            gst_app_src_end_of_stream(GST_APP_SRC(session.appsrc));
        }

        // Wait for EOS or timeout
        GstBus* bus = gst_element_get_bus(session.pipeline);
        GstMessage* msg = gst_bus_timed_pop_filtered(bus, GST_CLOCK_TIME_NONE,
                                                   (GstMessageType)(GST_MESSAGE_ERROR | GST_MESSAGE_EOS));
        if (msg) {
            gst_message_unref(msg);
        }
        gst_object_unref(bus);

        // Stop pipeline
        gst_element_set_state(session.pipeline, GST_STATE_NULL);
        gst_object_unref(session.pipeline);

        session.pipeline = nullptr;
        session.appsrc = nullptr;

        LOG_I("%s GStreamer pipeline cleaned up", session.stream_name.c_str());
    }
}

bool VideoControlService::push_frame_to_stream_pipeline(const Frame& frame,
                                                       StreamRecordingSession& session) {
    if (!session.appsrc || !frame.valid) {
        return false;
    }

    // TODO: Convert frame data to appropriate format for GStreamer
    // For now, create a placeholder buffer

    GstBuffer* buffer = gst_buffer_new_allocate(nullptr, frame.data.size(), nullptr);
    if (!buffer) {
        LOG_E("Failed to allocate GStreamer buffer for %s", session.stream_name.c_str());
        return false;
    }

    // Fill buffer with frame data
    GstMapInfo map;
    if (gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
        memcpy(map.data, frame.data.data(), frame.data.size());
        gst_buffer_unmap(buffer, &map);
    }

    // Set timestamp
    GST_BUFFER_PTS(buffer) = frame.timestamp * 1000; // Convert to nanoseconds

    // Push buffer to appsrc
    GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(session.appsrc), buffer);

    if (ret != GST_FLOW_OK) {
        LOG_W("Failed to push buffer to %s appsrc: %d", session.stream_name.c_str(), ret);
        return false;
    }

    // Update stream size statistics
    session.stream_size_bytes += frame.data.size();

    return true;
}

// Static callback functions for GStreamer
void VideoControlService::on_eos_message(GstBus* bus, GstMessage* message, gpointer user_data) {
    (void)bus;    // Suppress unused parameter warning
    (void)message; // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning
    LOG_I("GStreamer EOS received");
}

void VideoControlService::on_error_message(GstBus* bus, GstMessage* message, gpointer user_data) {
    (void)bus;    // Suppress unused parameter warning
    (void)user_data; // Suppress unused parameter warning

    GError* error = nullptr;
    gchar* debug_info = nullptr;
    gst_message_parse_error(message, &error, &debug_info);

    LOG_E("GStreamer error: %s", error ? error->message : "Unknown error");
    if (debug_info) {
        LOG_D("Debug info: %s", debug_info);
    }

    if (error) g_error_free(error);
    if (debug_info) g_free(debug_info);
}

// Utility methods
std::string VideoControlService::format_timestamp(uint64_t timestamp_us) {
    auto time_point = std::chrono::system_clock::time_point(std::chrono::microseconds(timestamp_us));
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::microseconds(timestamp_us)) % 1000;

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
        << "." << std::setfill('0') << std::setw(3) << ms.count();

    return oss.str();
}

std::string VideoControlService::format_duration(uint64_t duration_sec) {
    uint64_t hours = duration_sec / 3600;
    uint64_t minutes = (duration_sec % 3600) / 60;
    uint64_t seconds = duration_sec % 60;

    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << hours << ":"
        << std::setfill('0') << std::setw(2) << minutes << ":"
        << std::setfill('0') << std::setw(2) << seconds;

    return oss.str();
}

std::string VideoControlService::format_file_size(uint64_t size_bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(size_bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(1) << size << " " << units[unit_index];

    return oss.str();
}

bool VideoControlService::create_directory_if_not_exists(const std::string& path) {
    struct stat stat_buf;
    if (stat(path.c_str(), &stat_buf) == 0) {
        return S_ISDIR(stat_buf.st_mode);
    }

    // Create directory recursively
    std::string command = "mkdir -p " + path;
    int result = system(command.c_str());

    if (result == 0) {
        LOG_I("Created directory: %s", path.c_str());
        return true;
    } else {
        LOG_E("Failed to create directory: %s", path.c_str());
        return false;
    }
}

uint64_t VideoControlService::get_directory_size(const std::string& path) {
    uint64_t total_size = 0;

    DIR* dir = opendir(path.c_str());
    if (!dir) {
        return 0;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        std::string full_path = path + "/" + entry->d_name;
        struct stat stat_buf;
        if (stat(full_path.c_str(), &stat_buf) == 0) {
            if (S_ISREG(stat_buf.st_mode)) {
                total_size += stat_buf.st_size;
            } else if (S_ISDIR(stat_buf.st_mode)) {
                total_size += get_directory_size(full_path);
            }
        }
    }

    closedir(dir);
    return total_size;
}
